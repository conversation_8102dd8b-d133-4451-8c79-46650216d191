-- =====================================================
-- DATABASE QUERY EXAMPLES & USE CASES
-- Demonstrating the power of the improved schema
-- =====================================================

-- =====================================================
-- 1. VOICE COMMAND PROCESSING QUERIES
-- =====================================================

-- Query: "Gọi John sau meeting này"
-- Step 1: Find current meeting
WITH current_meeting AS (
    SELECT id, end_time
    FROM temporal_entities 
    WHERE user_id = $user_id 
      AND entity_type = 'event'
      AND start_time <= NOW() 
      AND end_time > NOW()
      AND status = 'active'
    ORDER BY start_time
    LIMIT 1
),
-- Step 2: Find John in contacts
john_contact AS (
    SELECT id, display_name, primary_phone
    FROM contacts 
    WHERE user_id = $user_id 
      AND (display_name ILIKE '%john%' OR first_name ILIKE '%john%')
      AND is_active = true
    ORDER BY importance_level DESC
    LIMIT 1
)
-- Step 3: Create reminder task
INSERT INTO temporal_entities (
    user_id, entity_type, title, start_time, 
    status, created_via, ai_confidence
)
SELECT 
    $user_id, 
    'reminder',
    'Call ' || jc.display_name,
    cm.end_time + INTERVAL '5 minutes',
    'active',
    'voice',
    0.95
FROM current_meeting cm, john_contact jc
RETURNING id;

-- Then link the contact
INSERT INTO temporal_entity_participants (temporal_entity_id, contact_id, role)
VALUES ($temporal_entity_id, $john_contact_id, 'target');

-- =====================================================
-- 2. LOCATION-BASED QUERIES
-- =====================================================

-- Query: "Nhắc tôi mua sữa khi ở siêu thị"
-- Step 1: Find or create grocery store places
WITH grocery_stores AS (
    SELECT id, name, latitude, longitude
    FROM places 
    WHERE user_id = $user_id 
      AND (category = 'grocery' OR name ILIKE '%siêu thị%' OR name ILIKE '%market%')
      AND is_active = true
),
-- Step 2: Create the reminder
new_reminder AS (
    INSERT INTO temporal_entities (
        user_id, entity_type, title, status, created_via
    ) VALUES (
        $user_id, 'reminder', 'Mua sữa', 'active', 'voice'
    ) RETURNING id
)
-- Step 3: Create location trigger for each grocery store
INSERT INTO location_triggers (
    user_id, place_id, trigger_name, trigger_type, 
    action_type, action_config, is_active
)
SELECT 
    $user_id,
    gs.id,
    'Grocery reminder: Mua sữa',
    'enter',
    'reminder',
    json_build_object(
        'temporal_entity_id', nr.id,
        'message', 'Bạn đã đến siêu thị. Nhớ mua sữa nhé!',
        'priority', 'high'
    ),
    true
FROM grocery_stores gs, new_reminder nr;

-- =====================================================
-- 3. INTELLIGENT SCHEDULING QUERIES
-- =====================================================

-- Query: "Lên lịch ăn trưa với Sarah tuần sau"
-- Step 1: Find Sarah
WITH sarah_contact AS (
    SELECT id, display_name, importance_level
    FROM contacts 
    WHERE user_id = $user_id 
      AND (display_name ILIKE '%sarah%' OR first_name ILIKE '%sarah%')
      AND is_active = true
    ORDER BY importance_level DESC
    LIMIT 1
),
-- Step 2: Find available lunch slots next week
available_slots AS (
    SELECT 
        generate_series(
            date_trunc('week', NOW()) + INTERVAL '1 week' + INTERVAL '12 hours',
            date_trunc('week', NOW()) + INTERVAL '2 weeks' + INTERVAL '14 hours',
            INTERVAL '1 day'
        ) AS slot_time
),
-- Step 3: Check for conflicts
conflict_free_slots AS (
    SELECT as_table.slot_time
    FROM available_slots as_table
    LEFT JOIN temporal_entities te ON (
        te.user_id = $user_id 
        AND te.start_time <= as_table.slot_time + INTERVAL '2 hours'
        AND te.end_time >= as_table.slot_time
        AND te.status = 'active'
    )
    WHERE te.id IS NULL
      AND EXTRACT(dow FROM as_table.slot_time) BETWEEN 1 AND 5 -- Weekdays only
    ORDER BY as_table.slot_time
    LIMIT 3
)
-- Step 4: Create lunch meeting
INSERT INTO temporal_entities (
    user_id, entity_type, title, start_time, end_time,
    status, created_via, ai_confidence
)
SELECT 
    $user_id,
    'event',
    'Ăn trưa với ' || sc.display_name,
    cfs.slot_time,
    cfs.slot_time + INTERVAL '1 hour',
    'active',
    'voice',
    0.88
FROM sarah_contact sc, conflict_free_slots cfs
LIMIT 1
RETURNING id;

-- =====================================================
-- 4. CONTEXT-AWARE QUERIES
-- =====================================================

-- Query: "Hôm nay tôi có gì?" - Today's agenda with context
SELECT 
    te.id,
    te.entity_type,
    te.title,
    te.start_time,
    te.end_time,
    te.status,
    te.priority,
    
    -- Participants information
    COALESCE(
        json_agg(
            json_build_object(
                'name', c.display_name,
                'phone', c.primary_phone,
                'relationship', c.relationship_type,
                'role', tep.role
            )
        ) FILTER (WHERE c.id IS NOT NULL), 
        '[]'::json
    ) as participants,
    
    -- Location information
    COALESCE(
        json_agg(
            DISTINCT json_build_object(
                'name', p.name,
                'address', p.address,
                'travel_time', tel.travel_time_minutes
            )
        ) FILTER (WHERE p.id IS NOT NULL),
        '[]'::json
    ) as locations,
    
    -- Weather and commute info would be added via application layer
    CASE 
        WHEN te.start_time IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (te.start_time - NOW())) / 60
        ELSE NULL 
    END as minutes_until_start

FROM temporal_entities te
LEFT JOIN temporal_entity_participants tep ON te.id = tep.temporal_entity_id
LEFT JOIN contacts c ON tep.contact_id = c.id AND c.is_active = true
LEFT JOIN temporal_entity_locations tel ON te.id = tel.temporal_entity_id
LEFT JOIN places p ON tel.place_id = p.id AND p.is_active = true

WHERE te.user_id = $user_id
  AND te.is_active = true
  AND te.status IN ('active', 'pending')
  AND (
    -- Today's scheduled items
    (te.start_time::date = CURRENT_DATE) OR
    -- Unscheduled tasks and reminders
    (te.start_time IS NULL AND te.entity_type IN ('task', 'reminder'))
  )

GROUP BY te.id, te.entity_type, te.title, te.start_time, te.end_time, te.status, te.priority
ORDER BY 
    CASE WHEN te.start_time IS NOT NULL THEN te.start_time ELSE '23:59:59'::time END,
    te.priority,
    te.created_at;

-- =====================================================
-- 5. RELATIONSHIP INTELLIGENCE QUERIES
-- =====================================================

-- Find people I haven't contacted recently but should
WITH contact_patterns AS (
    SELECT 
        c.id,
        c.display_name,
        c.relationship_type,
        c.importance_level,
        c.last_contact_at,
        c.contact_frequency_days,
        
        -- Calculate days since last contact
        COALESCE(
            EXTRACT(days FROM NOW() - c.last_contact_at),
            999
        ) as days_since_contact,
        
        -- Expected contact frequency based on relationship
        CASE c.relationship_type
            WHEN 'family' THEN 7
            WHEN 'close_friend' THEN 14
            WHEN 'friend' THEN 30
            WHEN 'work' THEN 21
            ELSE COALESCE(c.contact_frequency_days, 60)
        END as expected_frequency
        
    FROM contacts c
    WHERE c.user_id = $user_id 
      AND c.is_active = true
      AND c.importance_level >= 6 -- Only important contacts
)
SELECT 
    cp.display_name,
    cp.relationship_type,
    cp.days_since_contact,
    cp.expected_frequency,
    
    -- Urgency score (higher = more urgent)
    ROUND(
        (cp.days_since_contact::float / cp.expected_frequency) * cp.importance_level,
        2
    ) as urgency_score,
    
    -- Suggested action
    CASE 
        WHEN cp.days_since_contact > cp.expected_frequency * 2 THEN 'Call immediately'
        WHEN cp.days_since_contact > cp.expected_frequency * 1.5 THEN 'Schedule call this week'
        WHEN cp.days_since_contact > cp.expected_frequency THEN 'Send message'
        ELSE 'Up to date'
    END as suggested_action

FROM contact_patterns cp
WHERE cp.days_since_contact > cp.expected_frequency
ORDER BY urgency_score DESC
LIMIT 10;

-- =====================================================
-- 6. PATTERN LEARNING QUERIES
-- =====================================================

-- Detect user's routine patterns
WITH daily_patterns AS (
    SELECT 
        EXTRACT(hour FROM te.start_time) as hour_of_day,
        EXTRACT(dow FROM te.start_time) as day_of_week,
        te.entity_type,
        te.title,
        COUNT(*) as frequency,
        AVG(EXTRACT(EPOCH FROM (te.end_time - te.start_time))/60) as avg_duration_minutes
    
    FROM temporal_entities te
    WHERE te.user_id = $user_id
      AND te.start_time >= NOW() - INTERVAL '90 days'
      AND te.status = 'completed'
      AND te.entity_type = 'event'
    
    GROUP BY 
        EXTRACT(hour FROM te.start_time),
        EXTRACT(dow FROM te.start_time),
        te.entity_type,
        te.title
    
    HAVING COUNT(*) >= 3 -- At least 3 occurrences
)
-- Insert detected patterns
INSERT INTO user_patterns (
    user_id, pattern_type, pattern_name, pattern_data, confidence_score
)
SELECT 
    $user_id,
    'routine',
    'Daily ' || dp.title || ' at ' || dp.hour_of_day || ':00',
    json_build_object(
        'hour', dp.hour_of_day,
        'day_of_week', dp.day_of_week,
        'activity', dp.title,
        'duration_minutes', dp.avg_duration_minutes,
        'frequency_per_week', dp.frequency
    ),
    LEAST(dp.frequency / 10.0, 1.0) -- Confidence based on frequency
FROM daily_patterns dp
ON CONFLICT DO NOTHING;

-- =====================================================
-- 7. PROACTIVE SUGGESTIONS QUERIES
-- =====================================================

-- Generate proactive suggestions based on context
WITH current_context AS (
    SELECT 
        NOW() as current_time,
        EXTRACT(hour FROM NOW()) as current_hour,
        EXTRACT(dow FROM NOW()) as current_dow
),
-- Find relevant patterns for current time
relevant_patterns AS (
    SELECT up.*
    FROM user_patterns up, current_context cc
    WHERE up.user_id = $user_id
      AND up.pattern_type = 'routine'
      AND up.confidence_score > 0.6
      AND (up.pattern_data->>'hour')::int = cc.current_hour
      AND (up.pattern_data->>'day_of_week')::int = cc.current_dow
),
-- Check if user has already done this today
missing_routines AS (
    SELECT rp.*
    FROM relevant_patterns rp
    LEFT JOIN temporal_entities te ON (
        te.user_id = $user_id
        AND te.title ILIKE '%' || (rp.pattern_data->>'activity') || '%'
        AND te.start_time::date = CURRENT_DATE
        AND te.status IN ('active', 'completed')
    )
    WHERE te.id IS NULL
)
-- Generate suggestions
SELECT 
    'routine_reminder' as suggestion_type,
    'It''s ' || (mr.pattern_data->>'hour') || ':00 - time for ' || 
    (mr.pattern_data->>'activity') || '?' as suggestion_text,
    mr.pattern_data,
    mr.confidence_score,
    
    -- Suggested action
    json_build_object(
        'action', 'create_reminder',
        'title', mr.pattern_data->>'activity',
        'duration_minutes', mr.pattern_data->>'duration_minutes'
    ) as suggested_action

FROM missing_routines mr
ORDER BY mr.confidence_score DESC
LIMIT 3;

-- =====================================================
-- 8. ANALYTICS QUERIES
-- =====================================================

-- User productivity analytics
SELECT 
    DATE_TRUNC('week', te.start_time) as week,
    
    -- Task completion metrics
    COUNT(*) FILTER (WHERE te.entity_type = 'task') as total_tasks,
    COUNT(*) FILTER (WHERE te.entity_type = 'task' AND te.status = 'completed') as completed_tasks,
    ROUND(
        COUNT(*) FILTER (WHERE te.entity_type = 'task' AND te.status = 'completed')::float /
        NULLIF(COUNT(*) FILTER (WHERE te.entity_type = 'task'), 0) * 100,
        1
    ) as completion_rate_percent,
    
    -- Meeting metrics
    COUNT(*) FILTER (WHERE te.entity_type = 'event') as total_meetings,
    AVG(EXTRACT(EPOCH FROM (te.end_time - te.start_time))/60) 
        FILTER (WHERE te.entity_type = 'event') as avg_meeting_duration_minutes,
    
    -- Voice usage
    COUNT(*) FILTER (WHERE te.created_via = 'voice') as voice_created_items,
    ROUND(
        COUNT(*) FILTER (WHERE te.created_via = 'voice')::float / COUNT(*) * 100,
        1
    ) as voice_usage_percent

FROM temporal_entities te
WHERE te.user_id = $user_id
  AND te.start_time >= NOW() - INTERVAL '12 weeks'
  AND te.is_active = true

GROUP BY DATE_TRUNC('week', te.start_time)
ORDER BY week DESC;

-- =====================================================
-- 9. PERFORMANCE OPTIMIZATION QUERIES
-- =====================================================

-- Index usage analysis
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Query performance monitoring
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%temporal_entities%'
ORDER BY total_time DESC
LIMIT 10;
