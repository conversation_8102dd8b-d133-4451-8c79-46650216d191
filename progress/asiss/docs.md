# Personal AI Assistant - Product Requirements Document (PRD) v2.0

## **Executive Summary**

**Product Name:** AI Personal Assistant  
**Version:** 2.0 MVP (Simplified & Focused)  
**Target Launch:** Q2 2025  
**Platform:** Mobile-first (iOS/Android via Flutter) + Web companion

**Vision Statement:**  
*"Trợ lý duy nhất hiểu người, nơi và thói quen của bạn - quản lý cuộc sống qua chat tự nhiên"*

**Value Proposition:**  
Thay thế việc jonglering nhiều app bằng một AI agent thông minh hiểu ngữ cảnh cá nhân, danh bạ và vị trí của bạn.

---

## **Problem Statement Revised**

### **Core Pain Points:**
1. **Context Loss:** Apps không biết "John" là ai, "nhà" ở đâu, "thói quen" ra sao
2. **Mobile Friction:** Productivity apps thiết kế cho desktop, mobile experience kém
3. **Voice Gap:** Không có personal assistant thực sự hoạt động tốt bằng giọng nói
4. **Relationship Blindness:** Apps không hiểu mối quan hệ giữa người-sự kiện-địa điểm
5. **Fragmented Data:** Lịch, task, danh bạ, vị trí đều tách biệt

### **Market Opportunity:**
- Chưa có AI assistant voice-first cho personal organization
- Chưa có app hiểu context relationship (people + places + patterns)
- Mobile-first generation cần solution khác desktop tools

---

## **Target Audience**

### **Primary Persona - "Mobile Professional" (80% focus):**
- **Demographics:** 25-45 tuổi, thu nhập 1-5 tỷ/năm, sống trên điện thoại
- **Characteristics:** Bận rộn, di chuyển nhiều, multitasking
- **Pain Points:** Quên việc, lộn xộn lịch, khó manage relationships
- **Needs:** Voice control, context awareness, proactive assistance

### **Secondary Persona - "Family Organizer" (20% focus):**
- **Demographics:** 30-50 tuổi, quản lý gia đình + công việc
- **Characteristics:** Tổ chức nhiều người, nhiều commitment
- **Pain Points:** Tracking commitments cho cả gia đình
- **Needs:** Shared calendar, family reminders, appointment management

---

## **Simplified Feature Set**

### **Core MVP Features (Launch Month 1)**

#### **1. Intelligent Chat Interface**
```
User Experience:
- "Gọi John sau meeting này"
- "Nhắc tôi mua sữa khi ở siêu thị" 
- "Lên lịch ăn trưa với Sarah tuần sau"
- "Hôm nay tôi có gì?"

Technical Requirements:
- Voice-first input (primary interaction method)
- Context-aware responses using personal data
- Contact name recognition
- Location understanding
- Natural language time parsing (Vietnamese)
```

#### **2. Contact-Aware Calendar**
```
Core Functions:
- "Hẹn bác sĩ Nam thứ 3 tới" → Knows Dr. Nam's contact
- "Meeting với team marketing mai 2h" → Invites team members
- "Call mom after work" → Schedules after work hours

Features:
- Contact integration & relationship mapping
- Smart scheduling with conflict detection
- People-centric event organization
- Communication shortcuts
```

#### **3. Context-Smart Tasks**
```
Task Intelligence:
- "Nhắc tôi follow up với khách hàng A" → Creates task + sets contact context
- "Chuẩn bị presentation cho meeting với CEO" → Links to calendar event
- "Mua quà sinh nhật cho em" → Remembers relationship + important dates

Features:
- Relationship-aware task creation
- Event-linked task management
- People-based task organization
- Smart due date suggestions
```

#### **4. Location-Based Reminders**
```
Location Intelligence:
- "Nhắc tôi gọi khách khi rời văn phòng"
- "Mua thuốc khi qua hiệu thuốc"
- "Text vợ khi sắp về nhà"

Features:
- Geofence-based triggers
- Place recognition (home, office, gym)
- Commute-aware scheduling
- Location context for tasks
```

### **Essential Extensions (Month 2)**

#### **5. Voice Optimization**
```
Hands-Free Operation:
- Voice command shortcuts
- Offline voice processing
- Car integration ready
- Background voice activation

Features:
- "Hey Assistant" wake word
- Voice-only task completion
- Audio feedback for confirmations
- Quick voice capture notes
```

#### **6. Financial Awareness**
```
Bill & Subscription Management:
- "Nhắc tôi đóng tiền điện cuối tháng"
- "Track gia hạn Netflix"
- "Ghi chi phí lunch meeting hôm nay"

Features:
- Recurring bill reminders
- Subscription tracking
- Simple expense logging
- Payment due alerts
```

### **Advanced Features (Month 3-6)**

#### **7. Habit & Routine Support**
```
Daily Patterns:
- "Đã uống đủ nước chưa?"
- "Reminder tập gym như thường lệ"
- "Check-in with team như mỗi tuần"

Features:
- Routine detection & automation
- Habit streak tracking
- Pattern-based suggestions
- Health & wellness integration
```

#### **8. Travel & Event Planning**
```
Complex Planning:
- "Plan weekend trip to Đà Lạt"
- "Organize team outing for 10 people"
- "Prepare for business trip to Singapore"

Features:
- Multi-step planning workflows
- Group event coordination
- Travel itinerary management
- Preparation task automation
```

---

## **Simplified App Architecture**

### **3-Tab Navigation (Instead of 5)**

#### **Tab 1: Trợ Lý (Primary Interface)**
```dart
ChatScreen:
- Always-available chat interface
- Voice input prominent
- Quick action buttons
- Context-aware suggestions
- Recent conversation history
```

#### **Tab 2: Hôm Nay (Most Important)**
```dart
TodayScreen:
- Today's timeline (events + tasks)
- Completion progress
- People to contact today
- Location-based reminders
- Quick stats overview
```

#### **Tab 3: Kế Hoạch (Overview & Future)**
```dart
PlanningScreen:
- Calendar view (week/month)
- Upcoming tasks
- People & relationship overview
- Bills & subscription alerts
- Future planning space
```

### **Data Integration Priority**
```
Tier 1: Essential Integrations
✅ Device contacts (names, relationships)
✅ Device calendar (Google/Apple Calendar) 
✅ Device location services
✅ Voice recognition

Tier 2: Value-Add Integrations  
✅ Maps & Places (Google/Apple Maps)
✅ Communication apps (SMS, WhatsApp)
✅ Banking/Finance apps (bill reminders)

Tier 3: Advanced Integrations
✅ Health apps (fitness, medication)
✅ Travel apps (booking, navigation)
✅ Smart home devices
```

---

## **Technical Architecture Updated**

### **Backend Stack (Python-Focused)**
```
Core AI Agent:
- Python FastAPI + LangGraph
- OpenAI GPT-4 for reasoning
- Local models for privacy-sensitive operations
- PostgreSQL for structured data
- Redis for caching & sessions

Function Registry:
- Calendar operations (create, update, search)
- Contact operations (search, communication)
- Location operations (geofence, place recognition)
- Task operations (create, complete, prioritize)
- Communication operations (call, text, email)
```

### **Mobile App (Flutter)**
```
Key Features:
- Voice-first UI design
- Offline capability for basic functions
- Real-time sync across devices
- Background location processing
- Push notifications with smart timing
```

### **Privacy & Security**
```
Data Handling:
- Contact data encrypted at rest
- Location data processed locally when possible
- Conversation history with retention controls
- User consent for all data usage
- Export/delete functionality
```

---

## **User Stories - Revised**

### **Epic 1: Voice-First Personal Management**

**Story 1.1:** Voice Command Processing
```
As a busy professional
I want to manage my life using voice commands while driving/walking
So that I can stay organized without looking at my phone

Acceptance Criteria:
- Voice recognition works in noisy environments
- Can create calendar events by voice: "Meeting với John mai 2h"
- Can create tasks by voice: "Nhắc tôi gọi khách hàng sau meeting"
- Provides audio feedback for confirmations
- Works offline for basic commands
```

**Story 1.2:** Contact-Aware Commands
```
As a user with many personal & professional contacts
I want to use natural names in commands
So that I don't have to remember formal contact details

Acceptance Criteria:
- "Call mom" dials mother's number
- "Text John I'm running late" sends SMS to John
- "Meeting với team marketing" invites marketing team members
- Handles duplicate names intelligently
- Learns relationship contexts over time
```

### **Epic 2: Location-Intelligent Assistance**

**Story 2.1:** Location-Based Reminders
```
As a mobile user who's always on the go
I want reminders triggered by my location
So that I remember things at the right place and time

Acceptance Criteria:
- "Nhắc tôi mua sữa khi ở siêu thị" triggers at grocery stores
- "Call client khi rời văn phòng" triggers when leaving office
- Learns frequently visited places automatically
- Works with both specific addresses and place categories
- Respects battery life and privacy settings
```

### **Epic 3: Context-Aware Planning**

**Story 3.1:** Intelligent Scheduling
```
As someone managing multiple relationships and commitments
I want my assistant to understand context when scheduling
So that suggestions are relevant and practical

Acceptance Criteria:
- Suggests meeting times based on participants' past patterns
- Understands work hours vs personal time
- Factors in commute time for location-based events
- Warns about potential conflicts before they happen
- Proposes alternative times when conflicts detected
```

---

## **Database Schema - Simplified**

### **Core Tables**
```sql
-- Users (basic profile)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    timezone VARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Contacts (integrated from device)
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    relationship VARCHAR(50), -- 'family', 'work', 'friend', 'service'
    relationship_detail VARCHAR(100), -- 'mom', 'boss', 'dentist'
    importance_level INTEGER DEFAULT 5, -- 1-10 scale
    last_contact TIMESTAMP,
    notes TEXT,
    
    INDEX idx_user_contacts (user_id, relationship),
    INDEX idx_contact_search (user_id, name, phone)
);

-- Events (unified calendar + tasks)
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    type VARCHAR(20) NOT NULL, -- 'calendar', 'task', 'reminder'
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Time fields
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    is_all_day BOOLEAN DEFAULT FALSE,
    
    -- Location fields
    location VARCHAR(255),
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    location_trigger BOOLEAN DEFAULT FALSE,
    
    -- People involved
    contact_ids UUID[], -- Array of contact IDs
    created_via VARCHAR(20) DEFAULT 'chat', -- 'chat', 'voice', 'manual'
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    completed_at TIMESTAMP,
    
    -- External sync
    external_id VARCHAR(255),
    external_source VARCHAR(50),
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_user_events (user_id, type, start_time),
    INDEX idx_location_events (user_id, location_trigger),
    INDEX idx_contact_events (user_id, contact_ids)
);

-- Places (learned locations)
CREATE TABLE places (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL, -- 'home', 'office', 'gym', 'mom's house'
    address VARCHAR(500),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    radius INTEGER DEFAULT 100, -- meters
    place_type VARCHAR(50), -- 'home', 'work', 'frequent', 'service'
    visit_count INTEGER DEFAULT 0,
    last_visit TIMESTAMP,
    
    INDEX idx_user_places (user_id, place_type)
);

-- Conversations (chat history)
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    input_method VARCHAR(20) DEFAULT 'text', -- 'text', 'voice'
    context_used JSONB, -- Contacts, location, calendar context used
    functions_called JSONB, -- Functions executed
    created_at TIMESTAMP DEFAULT NOW(),
    
    INDEX idx_user_conversations (user_id, created_at DESC)
);
```

---

## **Development Timeline - Revised**

### **Phase 1: Foundation (Month 1)**

**Week 1-2: Core Chat + Basic Calendar**
```
Backend:
- FastAPI setup + basic LangGraph agent
- OpenAI integration for chat
- PostgreSQL database setup
- Basic calendar CRUD operations

Frontend:
- Flutter project setup
- Chat interface with voice input
- Basic calendar view
- Device calendar integration
```

**Week 3-4: Contact Integration + Location**
```
Backend:
- Contact import from device
- Relationship mapping
- Location services integration
- Basic geofencing

Frontend:
- Contact picker integration
- Location permission handling
- "Today" screen with timeline
- Voice command processing
```

### **Phase 2: Intelligence (Month 2)**

**Week 5-6: Smart Features**
```
Backend:
- Location-based reminder triggers
- Contact-aware event creation
- Natural language improvements
- Context learning algorithms

Frontend:
- Location-based notifications
- Contact suggestions in chat
- Improved voice recognition
- Background processing
```

**Week 7-8: Polish & Testing**
```
Backend:
- Performance optimization
- Error handling improvement
- Analytics & monitoring
- Security hardening

Frontend:
- UI/UX polish
- Offline functionality
- Beta testing program
- App store preparation
```

### **Phase 3: Enhancement (Month 3-6)**

**Month 3: Financial & Habits**
- Bill reminder system
- Subscription tracking
- Habit formation features
- Routine automation

**Month 4-5: Advanced Intelligence**
- Pattern recognition improvement
- Proactive suggestions
- Travel planning features
- Group event coordination

**Month 6: Scale & Polish**
- Performance optimization
- Advanced integrations
- Enterprise features preparation
- International expansion prep

---

## **Success Metrics - Updated**

### **Engagement Metrics**
- **Daily Voice Commands:** 5+ per active user
- **Context Recognition Rate:** 80%+ accurate contact/location understanding
- **Task Completion Rate:** 70%+ of created tasks completed
- **Session Frequency:** 3+ sessions per day average

### **Product-Market Fit Indicators**
- **Retention:** 70%+ weekly, 40%+ monthly
- **NPS Score:** > 60 within 90 days
- **Time to Value:** < 3 days to first successful voice command
- **Habit Formation:** 60%+ users active for 30+ consecutive days

### **Technical Performance**
- **Voice Recognition Accuracy:** > 90% in ideal conditions
- **Response Time:** < 2 seconds for 95% of requests
- **Offline Functionality:** Basic commands work without internet
- **Battery Impact:** < 5% additional daily drain

---

## **Competitive Differentiation**

### **Unique Value Props**
1. **Voice-First Mobile Design:** Unlike desktop-centric productivity tools
2. **Contact & Relationship Intelligence:** No other assistant understands "call mom"
3. **Location Context Awareness:** Proactive location-based assistance
4. **Vietnamese Cultural Context:** Understands Vietnamese time expressions & relationships

### **Competitive Advantages**
```
vs Google Assistant:
✅ Deep personal organization (not just queries)
✅ Contact relationship understanding
✅ Proactive planning assistance

vs Todoist/Things:
✅ Voice-first interaction
✅ Contact integration
✅ Location-based triggers

vs Calendar Apps:
✅ Natural language input
✅ Cross-domain intelligence (tasks + events)
✅ People-centric organization
```

---

## **Risk Assessment**

### **High Priority Risks**

**1. Voice Recognition Quality**
- **Risk:** Voice commands misunderstood, especially in Vietnamese contexts
- **Mitigation:** Extensive testing, fallback to text, learning from corrections

**2. Privacy Concerns**
- **Risk:** Users hesitant to share contacts, location, calendar data
- **Mitigation:** Transparent privacy policy, local processing options, user control

**3. Battery & Performance Impact**
- **Risk:** Location tracking and voice processing drain battery
- **Mitigation:** Efficient algorithms, user-configurable options, optimization

### **Medium Priority Risks**

**4. Contact Management Complexity**
- **Risk:** Duplicate contacts, relationship mapping errors
- **Mitigation:** Smart deduplication, user confirmation for relationships

**5. Platform Integration Challenges**
- **Risk:** iOS/Android permission restrictions, calendar sync issues
- **Mitigation:** Platform-specific implementations, graceful fallbacks

---

## **Go-to-Market Strategy**

### **Launch Strategy**
```
Phase 1: Private Beta (50 users)
- Friend & family testing
- Core functionality validation
- Initial feedback collection

Phase 2: Public Beta (500 users)  
- App store beta release
- Community feedback
- Performance testing

Phase 3: Full Launch (5000+ users)
- Product Hunt launch
- Social media campaign
- Influencer partnerships
```

### **Pricing Strategy**
```
Freemium Model:
- Free: 20 voice commands/day, basic features
- Premium: $9.99/month, unlimited usage, advanced features
- Family: $14.99/month, up to 4 accounts

Revenue Targets:
- Month 3: $5K MRR (500 premium users)
- Month 6: $25K MRR (2500 premium users)  
- Month 12: $100K MRR (10K premium users)
```

---

## **Conclusion**

This simplified, focused approach prioritizes daily mobile habits and context intelligence over feature breadth. By understanding people, places, and patterns, we create a defensible competitive advantage that generic productivity apps cannot easily replicate.

**Key Success Factors:**
1. **Voice-first mobile experience** that actually works
2. **Contact & location intelligence** that feels magical
3. **Daily habit formation** through consistent value delivery
4. **Privacy-first approach** to build user trust

**Next Steps:**
1. Validate core assumptions through user interviews
2. Build minimal technical proof-of-concept
3. Test voice interaction patterns with target users
4. Refine feature priority based on user feedback

---

**Document Version:** 2.0  
**Last Updated:** January 2025  
**Review Date:** February 2025  
**Stakeholders:** Product, Engineering, Design, Marketing