-- =====================================================
-- IMPROVED DATABASE SCHEMA FOR AI PERSONAL ASSISTANT
-- Version: 2.1 - Abstract & Scalable Design
-- =====================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis"; -- For advanced location features

-- =====================================================
-- CORE DOMAIN ENTITIES
-- =====================================================

-- Users table (minimal, focused)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    display_name VARCHAR(100),
    timezone VARCHAR(50) DEFAULT 'Asia/Ho_Chi_Minh',
    language_code VARCHAR(5) DEFAULT 'vi',
    
    -- Preferences as structured JSON
    preferences JSONB DEFAULT '{
        "voice_enabled": true,
        "location_tracking": true,
        "notification_quiet_hours": {"start": "22:00", "end": "07:00"},
        "default_reminder_time": 15
    }'::jsonb,
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_timezone CHECK (timezone IS NOT NULL)
);

-- =====================================================
-- PEOPLE & RELATIONSHIPS DOMAIN
-- =====================================================

-- Contacts with better relationship modeling
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Basic info
    display_name VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    nickname VARCHAR(100),
    
    -- Contact methods
    primary_phone VARCHAR(20),
    secondary_phone VARCHAR(20),
    primary_email VARCHAR(255),
    work_email VARCHAR(255),
    
    -- Relationship context
    relationship_type VARCHAR(50) NOT NULL DEFAULT 'acquaintance',
    relationship_detail VARCHAR(100),
    importance_level INTEGER DEFAULT 5 CHECK (importance_level BETWEEN 1 AND 10),
    
    -- Interaction tracking
    last_contact_at TIMESTAMP WITH TIME ZONE,
    contact_frequency_days INTEGER, -- How often they usually interact
    preferred_contact_method VARCHAR(20) DEFAULT 'phone', -- phone, email, text
    
    -- Metadata
    notes TEXT,
    tags TEXT[], -- For flexible categorization
    avatar_url VARCHAR(500),
    
    -- External sync
    external_id VARCHAR(255),
    external_source VARCHAR(50), -- 'google', 'apple', 'manual'
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    
    -- Indexes
    INDEX idx_user_contacts (user_id, is_active),
    INDEX idx_contact_search (user_id, display_name, first_name, last_name),
    INDEX idx_contact_relationship (user_id, relationship_type, importance_level),
    INDEX idx_contact_phone (user_id, primary_phone),
    
    -- Constraints
    CONSTRAINT valid_importance CHECK (importance_level BETWEEN 1 AND 10),
    CONSTRAINT valid_contact_method CHECK (preferred_contact_method IN ('phone', 'email', 'text', 'app'))
);

-- Contact groups for better organization
CREATE TABLE contact_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, name)
);

-- Many-to-many relationship for contacts and groups
CREATE TABLE contact_group_members (
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    group_id UUID REFERENCES contact_groups(id) ON DELETE CASCADE,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    PRIMARY KEY (contact_id, group_id)
);

-- =====================================================
-- LOCATION DOMAIN
-- =====================================================

-- Places with better geographic modeling
CREATE TABLE places (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Place identification
    name VARCHAR(255) NOT NULL,
    alias VARCHAR(100), -- "home", "office", "mom's place"
    address TEXT,
    
    -- Geographic data (using PostGIS for advanced features)
    location POINT, -- PostGIS point type
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    radius INTEGER DEFAULT 100, -- meters
    
    -- Place categorization
    place_type VARCHAR(50) NOT NULL, -- 'home', 'work', 'frequent', 'service', 'transit'
    category VARCHAR(50), -- 'restaurant', 'hospital', 'gym', etc.
    
    -- Usage patterns
    visit_count INTEGER DEFAULT 0,
    last_visit_at TIMESTAMP WITH TIME ZONE,
    average_stay_duration INTERVAL,
    typical_visit_times INTEGER[], -- Hours of day (0-23)
    
    -- Metadata
    notes TEXT,
    is_favorite BOOLEAN DEFAULT false,
    
    -- External data
    google_place_id VARCHAR(255),
    foursquare_id VARCHAR(255),
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    
    -- Indexes
    INDEX idx_user_places (user_id, place_type, is_active),
    INDEX idx_place_location (user_id, latitude, longitude),
    INDEX idx_place_alias (user_id, alias),
    
    -- Constraints
    CONSTRAINT valid_coordinates CHECK (
        latitude BETWEEN -90 AND 90 AND 
        longitude BETWEEN -180 AND 180
    ),
    CONSTRAINT valid_radius CHECK (radius > 0 AND radius <= 10000)
);

-- =====================================================
-- TEMPORAL DOMAIN (ABSTRACT EVENT SYSTEM)
-- =====================================================

-- Abstract base for all temporal entities
CREATE TABLE temporal_entities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Entity type and classification
    entity_type VARCHAR(50) NOT NULL, -- 'event', 'task', 'reminder', 'habit'
    category VARCHAR(50), -- 'meeting', 'personal', 'work', 'health'
    
    -- Core content
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Temporal properties
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    duration INTERVAL,
    is_all_day BOOLEAN DEFAULT false,
    
    -- Recurrence (using RRULE standard)
    recurrence_rule TEXT, -- RRULE format for complex recurrence
    recurrence_end TIMESTAMP WITH TIME ZONE,
    
    -- Status and completion
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'cancelled', 'postponed'
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage BETWEEN 0 AND 100),
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Priority and importance
    priority INTEGER DEFAULT 3 CHECK (priority BETWEEN 1 AND 5), -- 1=highest, 5=lowest
    urgency INTEGER DEFAULT 3 CHECK (urgency BETWEEN 1 AND 5),
    
    -- Context and metadata
    tags TEXT[],
    energy_level_required INTEGER CHECK (energy_level_required BETWEEN 1 AND 5),
    estimated_duration INTERVAL,
    
    -- Creation context
    created_via VARCHAR(20) DEFAULT 'chat', -- 'chat', 'voice', 'manual', 'system'
    ai_confidence DECIMAL(3,2), -- AI confidence in understanding (0.00-1.00)
    
    -- External sync
    external_id VARCHAR(255),
    external_source VARCHAR(50),
    sync_status VARCHAR(20) DEFAULT 'synced',
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    
    -- Indexes
    INDEX idx_user_temporal (user_id, entity_type, status, start_time),
    INDEX idx_temporal_time_range (user_id, start_time, end_time),
    INDEX idx_temporal_status (user_id, status, priority),
    INDEX idx_temporal_tags (user_id, tags),
    
    -- Constraints
    CONSTRAINT valid_time_range CHECK (
        (start_time IS NULL AND end_time IS NULL) OR
        (start_time IS NOT NULL AND (end_time IS NULL OR end_time >= start_time))
    ),
    CONSTRAINT valid_entity_type CHECK (entity_type IN ('event', 'task', 'reminder', 'habit', 'goal'))
);

-- =====================================================
-- RELATIONSHIP TABLES (MANY-TO-MANY)
-- =====================================================

-- People involved in temporal entities
CREATE TABLE temporal_entity_participants (
    temporal_entity_id UUID REFERENCES temporal_entities(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'participant', -- 'organizer', 'participant', 'optional', 'resource'
    response_status VARCHAR(20) DEFAULT 'pending', -- 'accepted', 'declined', 'tentative', 'pending'
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    PRIMARY KEY (temporal_entity_id, contact_id),
    INDEX idx_participant_contact (contact_id, response_status),
    INDEX idx_participant_entity (temporal_entity_id, role)
);

-- Location associations for temporal entities
CREATE TABLE temporal_entity_locations (
    temporal_entity_id UUID REFERENCES temporal_entities(id) ON DELETE CASCADE,
    place_id UUID REFERENCES places(id) ON DELETE CASCADE,
    location_type VARCHAR(50) DEFAULT 'primary', -- 'primary', 'alternative', 'transit_from', 'transit_to'
    travel_time_minutes INTEGER,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    PRIMARY KEY (temporal_entity_id, place_id, location_type),
    INDEX idx_location_entity (temporal_entity_id, location_type),
    INDEX idx_location_place (place_id)
);

-- Dependencies between temporal entities
CREATE TABLE temporal_entity_dependencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    predecessor_id UUID NOT NULL REFERENCES temporal_entities(id) ON DELETE CASCADE,
    successor_id UUID NOT NULL REFERENCES temporal_entities(id) ON DELETE CASCADE,
    dependency_type VARCHAR(50) DEFAULT 'finish_to_start', -- 'finish_to_start', 'start_to_start', 'finish_to_finish'
    lag_duration INTERVAL DEFAULT '0 minutes',
    is_hard_dependency BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(predecessor_id, successor_id),
    INDEX idx_dependency_predecessor (predecessor_id),
    INDEX idx_dependency_successor (successor_id),

    -- Prevent circular dependencies
    CONSTRAINT no_self_dependency CHECK (predecessor_id != successor_id)
);

-- =====================================================
-- LOCATION-BASED TRIGGERS
-- =====================================================

-- Geofence triggers for proactive assistance
CREATE TABLE location_triggers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    place_id UUID REFERENCES places(id) ON DELETE CASCADE,

    -- Trigger configuration
    trigger_name VARCHAR(255) NOT NULL,
    trigger_type VARCHAR(50) NOT NULL, -- 'enter', 'exit', 'dwell', 'approach'

    -- Conditions
    time_constraints JSONB, -- {"days": ["mon", "tue"], "hours": [9, 17]}
    dwell_time_minutes INTEGER, -- For 'dwell' triggers
    approach_distance_meters INTEGER, -- For 'approach' triggers

    -- Actions
    action_type VARCHAR(50) NOT NULL, -- 'reminder', 'notification', 'task_create', 'call_contact'
    action_config JSONB NOT NULL, -- Configuration for the specific action

    -- Status
    is_active BOOLEAN DEFAULT true,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    trigger_count INTEGER DEFAULT 0,

    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    INDEX idx_user_triggers (user_id, is_active),
    INDEX idx_place_triggers (place_id, trigger_type),

    CONSTRAINT valid_trigger_type CHECK (trigger_type IN ('enter', 'exit', 'dwell', 'approach')),
    CONSTRAINT valid_action_type CHECK (action_type IN ('reminder', 'notification', 'task_create', 'call_contact', 'send_message'))
);

-- =====================================================
-- CONVERSATION & AI CONTEXT
-- =====================================================

-- Conversation sessions for better context management
CREATE TABLE conversation_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Session metadata
    session_type VARCHAR(50) DEFAULT 'chat', -- 'chat', 'voice', 'proactive'
    device_info JSONB, -- Device type, OS, app version
    location_at_start POINT,

    -- Session lifecycle
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,

    -- Context summary
    topics_discussed TEXT[],
    entities_mentioned JSONB, -- {"contacts": [...], "places": [...], "events": [...]}
    session_summary TEXT,

    INDEX idx_user_sessions (user_id, started_at DESC),
    INDEX idx_active_sessions (user_id, is_active)
);

-- Individual messages within sessions
CREATE TABLE conversation_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES conversation_sessions(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Message content
    message_type VARCHAR(20) NOT NULL, -- 'user_text', 'user_voice', 'assistant_text', 'assistant_voice', 'system'
    content TEXT NOT NULL,

    -- Voice-specific fields
    audio_duration_seconds INTEGER,
    transcription_confidence DECIMAL(3,2),
    voice_language VARCHAR(5),

    -- AI processing
    intent_detected VARCHAR(100),
    entities_extracted JSONB,
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,

    -- Context used
    context_contacts UUID[],
    context_places UUID[],
    context_temporal_entities UUID[],

    -- Functions executed
    functions_called JSONB,
    function_results JSONB,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    INDEX idx_session_messages (session_id, created_at),
    INDEX idx_user_messages (user_id, created_at DESC),
    INDEX idx_message_type (session_id, message_type),

    CONSTRAINT valid_message_type CHECK (message_type IN ('user_text', 'user_voice', 'assistant_text', 'assistant_voice', 'system'))
);

-- =====================================================
-- ANALYTICS & LEARNING
-- =====================================================

-- User behavior patterns for AI learning
CREATE TABLE user_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

    -- Pattern identification
    pattern_type VARCHAR(50) NOT NULL, -- 'routine', 'preference', 'habit', 'relationship'
    pattern_name VARCHAR(255) NOT NULL,

    -- Pattern data
    pattern_data JSONB NOT NULL,
    confidence_score DECIMAL(3,2) NOT NULL,

    -- Temporal aspects
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_confirmed_at TIMESTAMP WITH TIME ZONE,
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,

    -- Usage
    times_applied INTEGER DEFAULT 0,
    success_rate DECIMAL(3,2),

    INDEX idx_user_patterns (user_id, pattern_type, confidence_score DESC),
    INDEX idx_pattern_validity (user_id, valid_from, valid_until)
);

-- =====================================================
-- SYSTEM TABLES
-- =====================================================

-- Audit log for important changes
CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),

    -- Action details
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- 'INSERT', 'UPDATE', 'DELETE'

    -- Change details
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],

    -- Context
    changed_by VARCHAR(50) DEFAULT 'system', -- 'user', 'system', 'ai_agent'
    change_reason VARCHAR(255),
    ip_address INET,
    user_agent TEXT,

    -- Timestamp
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    INDEX idx_audit_user (user_id, created_at DESC),
    INDEX idx_audit_table (table_name, record_id, created_at DESC),
    INDEX idx_audit_action (action, created_at DESC)
);

-- System configuration
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by VARCHAR(100) DEFAULT 'system'
);

-- =====================================================
-- VIEWS FOR COMMON QUERIES
-- =====================================================

-- Today's agenda view
CREATE VIEW user_today_agenda AS
SELECT
    te.id,
    te.user_id,
    te.entity_type,
    te.title,
    te.start_time,
    te.end_time,
    te.status,
    te.priority,
    COALESCE(
        json_agg(
            json_build_object(
                'id', c.id,
                'name', c.display_name,
                'role', tep.role
            )
        ) FILTER (WHERE c.id IS NOT NULL),
        '[]'::json
    ) as participants,
    COALESCE(
        json_agg(
            json_build_object(
                'id', p.id,
                'name', p.name,
                'address', p.address
            )
        ) FILTER (WHERE p.id IS NOT NULL),
        '[]'::json
    ) as locations
FROM temporal_entities te
LEFT JOIN temporal_entity_participants tep ON te.id = tep.temporal_entity_id
LEFT JOIN contacts c ON tep.contact_id = c.id
LEFT JOIN temporal_entity_locations tel ON te.id = tel.temporal_entity_id
LEFT JOIN places p ON tel.place_id = p.id
WHERE te.start_time::date = CURRENT_DATE
   OR (te.start_time IS NULL AND te.entity_type = 'task' AND te.status = 'active')
GROUP BY te.id, te.user_id, te.entity_type, te.title, te.start_time, te.end_time, te.status, te.priority;

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_places_updated_at BEFORE UPDATE ON places FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_temporal_entities_updated_at BEFORE UPDATE ON temporal_entities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function for audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, record_id, action, old_values, changed_by)
        VALUES (TG_TABLE_NAME, OLD.id, TG_OP, row_to_json(OLD), 'system');
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, record_id, action, old_values, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(OLD), row_to_json(NEW), 'system');
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, record_id, action, new_values, changed_by)
        VALUES (TG_TABLE_NAME, NEW.id, TG_OP, row_to_json(NEW), 'system');
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit triggers to important tables
CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_temporal_entities AFTER INSERT OR UPDATE OR DELETE ON temporal_entities FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- =====================================================
-- INITIAL DATA
-- =====================================================

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
('ai_models', '{"primary": "gpt-4", "fallback": "gpt-3.5-turbo", "voice": "whisper-1"}', 'AI model configurations'),
('feature_flags', '{"voice_enabled": true, "location_triggers": true, "proactive_suggestions": false}', 'Feature toggle flags'),
('rate_limits', '{"free_tier": {"voice_commands": 20, "api_calls": 100}, "premium_tier": {"voice_commands": -1, "api_calls": 1000}}', 'Rate limiting configuration');

-- =====================================================
-- PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Partitioning for large tables (conversation_messages)
-- This would be implemented based on actual usage patterns

-- Additional indexes for performance
CREATE INDEX CONCURRENTLY idx_temporal_entities_user_time ON temporal_entities (user_id, start_time) WHERE is_active = true;
CREATE INDEX CONCURRENTLY idx_conversation_messages_recent ON conversation_messages (user_id, created_at DESC) WHERE created_at > NOW() - INTERVAL '30 days';
CREATE INDEX CONCURRENTLY idx_places_location_gist ON places USING GIST (location) WHERE is_active = true;

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE users IS 'Core user profiles with preferences and settings';
COMMENT ON TABLE contacts IS 'User contacts with relationship intelligence and interaction tracking';
COMMENT ON TABLE temporal_entities IS 'Abstract base for all time-based entities (events, tasks, reminders, habits)';
COMMENT ON TABLE location_triggers IS 'Geofence-based triggers for proactive assistance';
COMMENT ON TABLE conversation_sessions IS 'Chat sessions for context management and analytics';
COMMENT ON TABLE user_patterns IS 'Learned user behavior patterns for AI personalization';

COMMENT ON COLUMN temporal_entities.recurrence_rule IS 'RRULE format for complex recurring patterns (RFC 5545)';
COMMENT ON COLUMN temporal_entities.ai_confidence IS 'AI confidence score (0.00-1.00) for natural language understanding';
COMMENT ON COLUMN location_triggers.action_config IS 'JSON configuration for trigger actions, schema varies by action_type';
